import { z } from "zod";

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1, "Password can't be empty"),
});

export const signupSchema = z
  .object({
    name: z.string().min(1, "Name can't be empty"),
    email: z.string().email(),
    password: z.string().min(8).max(20),
    confirmPassword: z.string().min(8).max(20),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirm"],
  });

export const userSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  email: z.string().email(),
  password: z.string().optional(),
});

export const mentorSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  image: z.string(),
  order: z.coerce.number().optional(),
  desc: z.string().min(1, "Desc can't be empty"),
  designation: z.string().min(1, "Designation can't be empty"),
  email: z.string().email(),
  password: z.string().optional(),
  linkedin: z.string().optional(),
  profile: z.string().optional(),
});
export const serviceSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  description: z.string().min(1, "Desc can't be empty"),
  price: z.coerce.number().min(1, "Price can't be empty"),
  meeting_link: z.string().url(),
});

export const blogSchema = z.object({
  title: z.string(),
  summary: z.string(),
  img: z.string(),
  blogger: z.string(),
  slug: z.string().optional(),
  desc: z.string(),
});

export const packageSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  note: z.string().min(1, "Note can't be empty"),
  amount: z.coerce.number().min(1, "Amount can't be empty"),
  order: z.coerce.number().optional(),
  service: z.array(z.string()),
});

export const immigrationSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  amount: z.coerce.number().min(1, "Amount can't be empty"),
  order: z.coerce.number().optional(),
  service: z.array(z.string()),
});
export const trainingSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  img: z.string(),
  order: z.coerce.number().optional(),
  amount: z.coerce.number().min(1, "Amount can't be empty"),
  service: z.array(z.string()),
  highlights: z.array(z.string()),
});
export const customerReviewSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  img: z.string().optional(),
  source: z.string(),
  comment: z.string(),
  date: z.date(),
  order: z.coerce.number().optional(),
  rating: z.coerce.number(),
});
