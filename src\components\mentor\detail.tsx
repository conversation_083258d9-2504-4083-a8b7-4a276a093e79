"use client";
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { imgUrl } from "@/utils/urls";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Edit } from "lucide-react";
import { useRouter } from "next/navigation";

const Detail = ({ data }: { data: IMentorInfo }) => {
  const router = useRouter();
  return (
    <Card>
      <CardContent className="flex justify-between items-center p-3">
        <div className="flex flex-col   w-full gap-4">
          <div className="relative w-full  h-[200px] lg:h-[300px] bg-[#f8f8f8] dark:bg-[#18181b] rounded-lg">
            <Image
              src={imgUrl + data.image}
              fill
              alt={data.name}
              unoptimized
              className="object-contain rounded-lg"
            />
          </div>
          <div>
            <div className="flex flex-col-reverse lg:flex-row justify-between gap-4">
              <div>
                <p className="text-xl text-center lg:text-start font-bold">
                  {data.name}
                </p>
                <p className="text-muted-foreground text-center lg:text-start">
                  {data.email}
                </p>
                <Badge
                  variant="secondary"
                  className="mt-2 break-words text-center"
                >
                  {data.designation}
                </Badge>
              </div>
              <Button onClick={() => router.push(`/mentor/${data.id}`)}>
                <Edit
                  className="-ms-1 me-2 opacity-60"
                  size={16}
                  strokeWidth={2}
                  aria-hidden="true"
                />
                Edit
              </Button>
            </div>

            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-2">About</h3>
              <div
                className="text-sm text-muted-foreground"
                dangerouslySetInnerHTML={{ __html: data.desc }}
              />
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              Member since: {new Date(data.createdAt).toLocaleDateString()}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Detail;
