"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Edit, Trash } from "lucide-react";
import { Dialog, DialogTrigger } from "../ui/dialog";
import { useRemoveTraining } from "@/hooks/use-query";
import TrainingForm from "./training-form";
import Image from "next/image";
import { imgUrl } from "@/utils/urls";
import { Badge } from "../ui/badge";

const Trainings = ({ data }: { data: ITraining[] }) => {
  const { mutate: remove, isPending } = useRemoveTraining();
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data.map((pkg) => (
        <Card className="w-full h-full flex flex-col" key={pkg.id}>
          <CardHeader className="space-y-3">
            <div className="w-full  relative h-[230px]">
              <Image
                src={imgUrl + pkg.img}
                fill
                alt="training"
                unoptimized
                className="object-cover w-full rounded-md"
              />
            </div>
            <div>
              <div className="flex justify-between w-full  items-center">
                <CardTitle className="text-2xl font-semibold">
                  {pkg.name}
                </CardTitle>
                {pkg?.order && (
                  <Badge className=" bg-[#404BD0]/10 text-[#404BD0] hover:bg-[#404BD0]/10 hover:text-[#404BD0]">
                    {pkg.order}
                  </Badge>
                )}
              </div>
              <div className="flex items-baseline gap-1">
                <span className="text-lg font-semibold">€{pkg.amount}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="mt-2 flex-grow space-y-5">
            <ul className="space-y-3">
              {pkg.service.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                  <span>{service}</span>
                </li>
              ))}
            </ul>
            <div className="space-y-2">
              <h2 className="font-semibold text-lg">Key Highlights</h2>
              <ul className="space-y-3">
                {pkg.highlights.map((service, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>{service}</span>
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
          <CardFooter className="justify-end gap-x-4">
            <Button
              className="bg-[#ffebe6] text-[#fd381d] hover:bg-[#ffebe6] "
              disabled={isPending}
              onClick={() => remove(pkg.id as string)}
            >
              <Trash
                className="-ms-1 me-2 opacity-60"
                size={16}
                strokeWidth={2}
                aria-hidden="true"
              />
              Delete
            </Button>
            <Dialog>
              <DialogTrigger>
                <Button>
                  <Edit
                    className="-ms-1 me-2 opacity-60"
                    size={16}
                    strokeWidth={2}
                    aria-hidden="true"
                  />
                  Edit
                </Button>
              </DialogTrigger>
              <TrainingForm training={pkg} />
            </Dialog>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default Trainings;
