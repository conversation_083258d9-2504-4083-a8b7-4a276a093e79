import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "../ui/card";
import { Star } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { formatCurrency, isValidUrl } from "@/utils/tools";
import { imgUrl } from "@/utils/urls";

const Mentors = ({ mentors }: { mentors: <PERSON><PERSON>[] }) => {
  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>Top Rated Mentors</CardTitle>
        <CardDescription>
          Mentors with highest ratings and revenue
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {mentors.map((mentor) => (
            <div key={mentor.id} className="flex items-center">
              <Avatar className="h-9 w-9 mr-2">
                <AvatarImage
                  src={
                    isValidUrl(mentor.image || "")
                      ? `${mentor.image}`
                      : `${imgUrl}${mentor.image}`
                  }
                  alt={mentor.name}
                />
                <AvatarFallback className="uppercase">
                  {mentor.name.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="ml-4 space-y-1">
                <p className="text-sm font-medium leading-none">
                  {mentor.name}
                </p>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Star className="mr-1 h-4 w-4" />
                  {mentor.average_rating} ({mentor.review_count} reviews)
                </div>
              </div>
              <div className="ml-auto font-medium">
                {formatCurrency(mentor.revenue_generated)}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default Mentors;
