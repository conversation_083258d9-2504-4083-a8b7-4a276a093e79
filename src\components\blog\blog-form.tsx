"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import FileUpload from "../common/file-upload";
import { blogSchema } from "@/utils/schema";
import { useCreateBlog, useUpdateBlog } from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";
import { User2Icon } from "lucide-react";
import { Textarea } from "../ui/textarea";
import Editor from "./editor";
import { blogEditorTemplate } from "@/utils/data";

interface IBlogProp {
  blog: IBlog | undefined;
}

const BlogForm: React.FC<IBlogProp> = ({ blog }) => {
  const form = useForm<z.infer<typeof blogSchema>>({
    resolver: zod<PERSON>esolver(blogSchema),
    defaultValues: {
      title: blog?.title,
      summary: blog?.summary,
      blogger: blog?.blogger,
      img: blog?.img,
      desc: blog ? blog.desc : blogEditorTemplate,
    },
  });
  const { mutate: create, isPending: isCreating } = useCreateBlog();
  const { mutate: update, isPending: isUpdating } = useUpdateBlog(
    blog?.id || ""
  );
  const onSubmit = (data: z.infer<typeof blogSchema>) => {
    if (blog) {
      update(data);
    } else {
      create(data);
    }
  };
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-8">
        <FileUpload
          form={form}
          field="img"
          message="PNG,JPEG and WEBP are allowed"
          accept=".png,.jpeg,.webp"
          folder="blog"
        />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Title</FormLabel>
                <FormControl>
                  <Input placeholder="Blog Title" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="blogger"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Author</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      id="input-09"
                      className="peer ps-9"
                      placeholder="Author Name"
                      {...field}
                    />
                    <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
                      <User2Icon size={16} strokeWidth={2} aria-hidden="true" />
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="summary"
          render={({ field }) => (
            <FormItem className="col-span-2">
              <FormLabel>Summary</FormLabel>
              <FormControl>
                <Textarea
                  id="textarea-07"
                  className="border-transparent bg-muted shadow-none"
                  rows={5}
                  placeholder="Leave a summary about blog"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="space-y-2">
          <FormLabel>Blog Editor</FormLabel>
          <Editor form={form} />
        </div>

        {isCreating || isUpdating ? (
          <ButtonLoader />
        ) : (
          <Button type="submit">Submit</Button>
        )}
      </form>
    </Form>
  );
};

export default BlogForm;
