import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Button } from "../ui/button";
import { Trash } from "lucide-react";
import { useRemoveUser } from "@/hooks/use-query";

const DeleteUser = ({ id }: { id: string }) => {
  const { mutate: remove, isPending } = useRemoveUser();
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="none"
            className="bg-[#ffebe6] text-[#fd381d]"
            disabled={isPending}
            onClick={() => remove(id)}
            size="icon"
            aria-label="Add new item"
          >
            <Trash size={16} strokeWidth={2} aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="bg-[#ffebe6] text-[#fd381d] text-center w-40">
          This action will permanently remove the user.
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default DeleteUser;
