import { IdCardIcon } from "@radix-ui/react-icons";
import {
  BookTextIcon,
  EuroIcon,
  Headset,
  LayoutGrid,
  Package,
  SquareArrowDown,
  StarIcon,
  User,
  UserCircle,
} from "lucide-react";

type Submenu = {
  href: string;
  label: string;
  active: boolean;
};

type Menu = {
  href: string;
  label: string;
  active: boolean;
  icon: any;
  submenus: Submenu[];
};

type Group = {
  groupLabel: string;
  menus: Menu[];
};

export function getMenuList(pathname: string): Group[] {
  return [
    {
      groupLabel: "",
      menus: [
        {
          href: "/",
          label: "Dashboard",
          active: pathname === "/",
          icon: LayoutGrid,
          submenus: [],
        },
      ],
    },
    {
      groupLabel: "Contents",
      menus: [
        {
          href: "/user",
          label: "User",
          active: pathname === "/user",
          icon: User,
          submenus: [],
        },
        {
          href: "/mentor",
          label: "Mentor",
          active: pathname === "/mentor",
          icon: UserCircle,
          submenus: [],
        },
        {
          href: "/our-package",
          label: "Our Packages",
          active: pathname === "/our-package",
          icon: Package,
          submenus: [],
        },
        {
          href: "/immigration",
          label: "Immigration Services",
          active: pathname === "/immigration",
          icon: IdCardIcon,
          submenus: [],
        },
        {
          href: "/training",
          label: "Training",
          active: pathname === "/training",
          icon: SquareArrowDown,
          submenus: [],
        },
        {
          href: "/blog",
          label: "Blog",
          active: pathname === "/blog",
          icon: BookTextIcon,
          submenus: [],
        },
        {
          href: "/guest",
          label: "Guest Purchase",
          active: pathname === "/guest",
          icon: EuroIcon,
          submenus: [],
        },
        {
          href: "/customer-review",
          label: "Customer review",
          active: pathname === "/customer-review",
          icon: StarIcon,
          submenus: [],
        },
        {
          href: "/contact-us",
          label: "Contact Us",
          active: pathname === "/contact-us",
          icon: Headset,
          submenus: [],
        },
      ],
    },
  ];
}
