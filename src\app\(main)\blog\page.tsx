import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { PlusIcon } from "lucide-react";
import { getBlogs } from "@/hooks/use-server";
import BlogDataTable from "@/components/table/blog/blog-datatable";
import { blogColumns } from "@/components/table/blog/blog-columns";

const BlogPage = async () => {
  const data = await getBlogs();
  return (
    <ContentLayout title="Blog">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Blog</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Link
          href="/blog/create"
          className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black"
        >
          <PlusIcon />
        </Link>
      </div>
      <Content>
        <BlogDataTable data={data} columns={blogColumns} />
      </Content>
    </ContentLayout>
  );
};

export default BlogPage;
