/* eslint-disable no-unused-vars */
interface IMentor {
  id?: string;
  name: string;
  email: string;
  image: string;
  desc: string;
  order?: number;
  password?: string;
  linkedin?: string;
  profile?: string;
  designation: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface User {
  id: string;
  name: string;
  email: string;
  image: string | null;
}

interface ServiceUser {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  user: User;
}

interface MentorInfoService {
  id: string;
  createdAt: string;
  description: string;
  meeting_link: string;
  name: string;
  price: number;
  updatedAt: string;
  status: string;
  users: ServiceUser[];
}

interface IMentorInfo {
  id: string;
  name: string;
  email: string;
  image: string;
  location: string;
  designation: string;
  desc: string;
  status: string;
  total_revenue: string;
  reviews: IReview[];
  services: MentorInfoService[];
  createdAt: Date;
  updatedAt: Date;
}

interface IService {
  id?: string;
  name: string;
  price: number;
  description: string;
  meeting_link: string;
  status?: Status;
  createdAt?: Date;
  updatedAt?: Date;
}

interface IReview {
  id: string;
  message: string;
  rating: number;
  mentor: IMentor;
  user: IUser;
  createdAt: Date;
  updatedAt: Date;
}

interface Mentor {
  id: string;
  name: string;
  image: string;
}

interface MentorService {
  price: number;
  name: string;
  id: string;
  meeting_link: string;
  mentor: Mentor;
}

interface Service {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  mentor_services: MentorService;
}

interface Package {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  package: {
    amount: number;
    name: string;
    id: string;
  };
}
interface Training {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  training: {
    amount: number;
    name: string;
    id: string;
  };
}

interface ImmigrationService {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  immigration_service: {
    amount: number;
    name: string;
    id: string;
  };
}

interface IUser {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  password?: string | null;
  createdAt: Date;
  updatedAt: Date;
  provider: string;
  total_spent: string;
  reviews: IReview[];
  services: Service[];
  packages: Package[];
  training: Training[];
  immigration_services: ImmigrationService[];
}

interface IContactUs {
  id: string;
  name: string;
  email: string;
  mobile: string;
  message: string;
  createdAt: Date;
  updatedAt: Date;
}

interface IBlog {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  desc: string;
  title: string;
  slug?: string;
  summary: string;
  blogger: string;
  img: string;
}

interface IPackage {
  id?: string;
  name: string;
  note: string;
  amount: number;
  order?: number;
  service: string[];
  createdAt?: Date;
  updatedAt?: Date;
}
interface IImmigration {
  id?: string;
  name: string;
  amount: number;
  order?: number;
  service: string[];
  createdAt?: Date;
  updatedAt?: Date;
}
interface ITraining {
  id?: string;
  name: string;
  amount: number;
  img: string;
  order?: number;
  service: string[];
  highlights: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

interface Mentor {
  id: string;
  name: string;
  image: string;
  order?: number;
  average_rating: number;
  review_count: string;
  revenue_generated: string;
  total_clients: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  image: string | null;
  createdAt: string;
}

interface Contact {
  id: string;
  name: string;
  email: string;
  message: string;
  createdAt: string;
}

interface IDashboard {
  total_mentors: string;
  total_users: string;
  mentor_service_revenue: string;
  package_revenue: string;
  immigration_service_revenue: string;
  training_revenue: string;
  total_revenue: string;
  top_rated_mentors: Mentor[];
  latest_users: User[];
  latest_contacts: Contact[];
}

interface IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
}
interface IGuestService extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  mentor_services: MentorService;
}
interface IGuestImmigration extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  immigration_service: {
    amount: number;
    name: string;
    id: string;
  };
}
interface IGuestTraining extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  training: {
    amount: number;
    name: string;
    id: string;
  };
}
interface IGuestPackage extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  package: {
    amount: number;
    name: string;
    id: string;
  };
}

interface ICustomerReview {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  name: string;
  img?: string;
  comment: string;
  source: string;
  rating: number;
  order?: number;
  date: Date;
}

interface IComment {
  id: string;
  content: string;
  blogId: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  parentId: string | null;
  author: {
    name: string;
    image: string | null;
  };
  replies?: IComment[];
}
