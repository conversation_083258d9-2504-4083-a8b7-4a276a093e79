"use client";
import React from "react";
import { <PERSON><PERSON><PERSON>ontent, DialogHeader } from "../ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { immigrationSchema } from "@/utils/schema";
import { DialogTitle } from "@radix-ui/react-dialog";
import { CircleX } from "lucide-react";
import { useCreateImmigration, useUpdateImmigration } from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";

interface IImmigrationProp {
  immigration: IImmigration | undefined;
}

const ImmigrationForm: React.FC<IImmigrationProp> = ({ immigration }) => {
  const [service, setService] = React.useState("");
  const form = useForm<z.infer<typeof immigrationSchema>>({
    resolver: zodResolver(immigrationSchema),
    defaultValues: {
      name: immigration?.name,
      amount: immigration?.amount,
      order: immigration
        ? immigration.order === null
          ? undefined
          : immigration.order
        : undefined,
      service: immigration ? immigration.service : [],
    },
  });
  const { mutate: create, isPending: isCreating } = useCreateImmigration();
  const { mutate: update, isPending: isUpdating } = useUpdateImmigration(
    immigration?.id || ""
  );
  const onSubmit = (data: z.infer<typeof immigrationSchema>) => {
    if (immigration) {
      update(data);
    } else {
      create(data);
    }
  };
  return (
    <DialogContent className="max-w-3xl max-h-[60vh] overflow-y-scroll scrollbar scrollbar-w-0">
      <DialogHeader>
        <DialogTitle>
          {immigration
            ? "Edit Immigration Service"
            : "Add New Immigration Service"}
        </DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Package Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <div className="relative flex rounded-lg shadow-sm shadow-black/5">
                      <span className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-sm text-muted-foreground">
                        €
                      </span>
                      <Input
                        id="input-16"
                        className="-me-px rounded-e-none ps-6 shadow-none"
                        placeholder="0.00"
                        type="number"
                        {...field}
                      />
                      <span className="-z-10 inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-sm text-muted-foreground">
                        EUR
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="order"
              render={({ field }) => (
                <FormItem className="lg:col-span-2 ">
                  <FormLabel>Order</FormLabel>
                  <FormControl>
                    <Input placeholder="1" min={1} type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="lg:col-span-2 space-y-2">
              <FormLabel>Services</FormLabel>
              <div className="flex rounded-lg shadow-sm shadow-black/5">
                <Input
                  className="-me-px flex-1 rounded-e-none shadow-none focus-visible:z-10"
                  value={service}
                  onChange={(e) => setService(e.target.value)}
                  placeholder="Improvement Feedback"
                />
                <button
                  type="button"
                  className="inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-sm font-medium text-foreground outline-offset-2 transition-colors hover:bg-accent hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={() => {
                    if (service.length > 0) {
                      form.setValue("service", [
                        ...form.watch("service"),
                        service,
                      ]);
                      setService("");
                    }
                  }}
                >
                  Add
                </button>
              </div>

              <div className="space-y-3">
                {form.watch("service").map((el, i) => (
                  <div
                    key={i}
                    className="bg-[#fafafa] dark:bg-[#18181b] p-2 rounded-md shadow-md flex relative"
                  >
                    <p>{el}</p>
                    <CircleX
                      className="absolute right-2 cursor-pointer text-red-400"
                      onClick={() => {
                        form.setValue("service", [
                          ...form
                            .watch("service")
                            .filter((item, index) => index !== i),
                        ]);
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
          {isCreating || isUpdating ? (
            <ButtonLoader />
          ) : (
            <Button>Submit</Button>
          )}
        </form>
      </Form>
    </DialogContent>
  );
};

export default ImmigrationForm;
