"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

export const contactUsColumns: ColumnDef<IContactUs>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "mobile",
    header: "Mobile",
  },
  {
    id: "message",
    header: "Message",
    cell: ({ row }) => {
      return (
        <p className="max-w-sm text-wrap break-words">{row.original.message}</p>
      );
    },
  },
  {
    id: "Created At",
    header: () => "",
    cell: ({ row }) => {
      return (
        <p>
          {format(new Date(row.original.createdAt as Date), "PPP 'at' HH:mm")}
        </p>
      );
    },
  },
];
