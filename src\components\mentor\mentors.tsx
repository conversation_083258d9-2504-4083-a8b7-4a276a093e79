"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { imgUrl } from "@/utils/urls";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ArrowRight, Edit, Trash } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { useDeleteMentor } from "@/hooks/use-query";
import { Badge } from "../ui/badge";

const Mentors = ({ data }: { data: IMentor[] }) => {
  const router = useRouter();
  const { mutate: remove, isPending } = useDeleteMentor();
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data.map((mentor) => (
        <Card className="flex flex-col h-full relative" key={mentor.id}>
          <CardHeader className="flex flex-row items-center gap-4 pb-2">
            <Avatar className="w-16 h-16">
              <AvatarImage src={imgUrl + mentor.image} alt={mentor.name} />
              <AvatarFallback>{mentor.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold">{mentor.name}</h3>
              <p className="text-sm text-muted-foreground">
                {mentor.designation}
              </p>
            </div>
          </CardHeader>
          <CardContent className="flex-grow">
            <div
              className="text-sm text-muted-foreground prose line-clamp-4"
              dangerouslySetInnerHTML={{ __html: mentor.desc }}
            />
          </CardContent>
          <CardFooter className=" w-full justify-between ">
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="none"
                    className="bg-[#ffebe6] text-[#fd381d]"
                    disabled={isPending}
                    onClick={() => remove(mentor.id as string)}
                    size="icon"
                    aria-label="Add new item"
                  >
                    <Trash size={16} strokeWidth={2} aria-hidden="true" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-[#ffebe6] text-[#fd381d] text-center w-40">
                  This action will permanently remove the mentor.
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <div className="flex items-center gap-x-4">
              <Button onClick={() => router.push(`/mentor/${mentor.id}`)}>
                <Edit
                  className="-ms-1 me-2 opacity-60"
                  size={16}
                  strokeWidth={2}
                  aria-hidden="true"
                />
                Edit
              </Button>

              <Button
                className="group"
                onClick={() => router.push(`/mentor/detail?id=${mentor.id}`)}
              >
                View
                <ArrowRight
                  className="-me-1 ms-2 opacity-60 transition-transform group-hover:translate-x-0.5"
                  size={16}
                  strokeWidth={2}
                  aria-hidden="true"
                />
              </Button>
            </div>
          </CardFooter>
          {mentor?.order && (
            <Badge className=" bg-[#404BD0]/10 text-[#404BD0] hover:bg-[#404BD0]/10 hover:text-[#404BD0] absolute top-2 right-2">
              {mentor.order}
            </Badge>
          )}
        </Card>
      ))}
    </div>
  );
};

export default Mentors;
