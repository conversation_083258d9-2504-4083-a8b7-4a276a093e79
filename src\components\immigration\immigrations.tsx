"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Edit, Trash } from "lucide-react";
import { Dialog, DialogTrigger } from "../ui/dialog";
import ImmigrationForm from "./immigration-form";
import { useRemoveImmigration } from "@/hooks/use-query";
import { Badge } from "../ui/badge";

const Immigrations = ({ data }: { data: IImmigration[] }) => {
  const { mutate: remove, isPending } = useRemoveImmigration();
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data.map((pkg) => (
        <Card className="w-full h-full flex flex-col relative" key={pkg.id}>
          <CardHeader className="space-y-1">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl font-bold">{pkg.name}</CardTitle>
              </div>
            </div>
            <div className="flex items-baseline gap-1">
              <span className="text-3xl font-bold">€{pkg.amount}</span>
            </div>
          </CardHeader>
          <CardContent className="mt-4 flex-grow">
            <ul className="space-y-3">
              {pkg.service.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                  <span>{service}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter className="justify-end gap-x-4">
            <Button
              className="bg-[#ffebe6] text-[#fd381d] hover:bg-[#ffebe6] "
              disabled={isPending}
              onClick={() => remove(pkg.id as string)}
            >
              <Trash
                className="-ms-1 me-2 opacity-60"
                size={16}
                strokeWidth={2}
                aria-hidden="true"
              />
              Delete
            </Button>
            <Dialog>
              <DialogTrigger>
                <Button>
                  <Edit
                    className="-ms-1 me-2 opacity-60"
                    size={16}
                    strokeWidth={2}
                    aria-hidden="true"
                  />
                  Edit
                </Button>
              </DialogTrigger>
              <ImmigrationForm immigration={pkg} />
            </Dialog>
          </CardFooter>
          {pkg?.order && (
            <Badge className=" bg-[#404BD0]/10 text-[#404BD0] hover:bg-[#404BD0]/10 hover:text-[#404BD0] absolute top-2 right-2">
              {pkg.order}
            </Badge>
          )}
        </Card>
      ))}
    </div>
  );
};

export default Immigrations;
