import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { PlusIcon } from "lucide-react";
import { NoResults } from "@/loader/no-results";
import { getCustomerReviews } from "@/hooks/use-server";
import CustomerDataTable from "@/components/table/customer-review/customer-datatable";
import { customerColumns } from "@/components/table/customer-review/customer-columns";

const CustomerReview = async () => {
  const data = await getCustomerReviews();
  return (
    <ContentLayout title="Customer Reviews">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Customer Reviews</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Link
          href="/customer-review/create"
          className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black"
        >
          <PlusIcon />
        </Link>
      </div>
      <Content>
        {data.length === 0 && (
          <NoResults
            title="There no Customer Review in database"
            description="Please click on plus icon to add review"
            className="min-h-[calc(100vh-56px-64px-20px-24px-56px-48px)]"
          />
        )}
        <CustomerDataTable data={data} columns={customerColumns} />
      </Content>
    </ContentLayout>
  );
};

export default CustomerReview;
