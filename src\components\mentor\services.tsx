"use client";
import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";
import { Dialog, DialogTrigger } from "../ui/dialog";
import { Calendar, Edit, PlusIcon, Trash2 } from "lucide-react";
import ServiceForm from "../services/service-form";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";
import { useDeleteService } from "@/hooks/use-query";
import { Badge } from "../ui/badge";
import { NoResults } from "@/loader/no-results";

const Services = ({ data }: { data: IMentorInfo }) => {
  const router = useRouter();
  const { mutate: remove, isPending: isDeleting } = useDeleteService();
  return (
    <Card>
      <CardHeader className="font-bold flex flex-row items-center justify-between">
        <p className="text-xl">Services</p>
        <Dialog>
          <DialogTrigger className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black">
            <PlusIcon />
          </DialogTrigger>
          <ServiceForm service={undefined} id={data.id} />
        </Dialog>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data.services.length === 0 && (
          <NoResults
            title="There are No services for this mentor"
            description="Click on plus icon to add services"
            className="col-span-3"
          />
        )}
        {data.services.map((service) => (
          <Card
            key={service.id}
            className="w-full flex flex-col justify-between"
          >
            <div>
              <CardHeader>
                <CardTitle>{service.name}</CardTitle>

                <CardDescription className="space-y-2">
                  <Badge
                    variant="secondary"
                    className="mt-2 break-words text-center inline-flex"
                  >
                    {new Date(data.createdAt).toLocaleDateString()}
                  </Badge>
                  <p>€{service.price}</p>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{service.description}</p>
              </CardContent>
            </div>
            <CardFooter className="flex justify-between">
              <div className="space-x-4">
                <Dialog>
                  <DialogTrigger>
                    <Button variant="outline" size="icon">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <ServiceForm
                    service={service as unknown as IService}
                    id={data.id}
                  />
                </Dialog>

                <Button
                  variant="outline"
                  size="icon"
                  disabled={isDeleting}
                  onClick={() => remove(service.id || "")}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <Button
                variant="default"
                onClick={() => router.push(service.meeting_link)}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Book Meeting
              </Button>
            </CardFooter>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
};

export default Services;
