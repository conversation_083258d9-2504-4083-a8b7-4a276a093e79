import type { Metada<PERSON> } from "next";
import "../globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import React from "react";
import AdminPanelLayout from "@/components/admin-panel/admin-panel-layout";
import { GeistSans } from "geist/font/sans";
import NextAuthProvider from "@/provider/next-auth";
import TanStackProvider from "@/provider/tanstack";

export const metadata: Metadata = {
  title: "Admin Panel",
  description: "Careerireland admin panel",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={GeistSans.className}>
        {" "}
        <NextAuthProvider>
          <TanStackProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem
              disableTransitionOnChange
            >
              <AdminPanelLayout>{children}</AdminPanelLayout>
              <Toaster position="top-center" />
            </ThemeProvider>
          </TanStackProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
