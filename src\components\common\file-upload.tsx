"use client";
import { FileImage } from "lucide-react";
import React from "react";
import { FormField, FormMessage } from "@/components/ui/form";
import { ReloadIcon } from "@radix-ui/react-icons";
import Image from "next/image";
import { imgUrl } from "@/utils/urls";
import { uploadMedia } from "@/hooks/use-media";

interface IFileUploadProp {
  form: any;
  field: any;
  message: string;
  accept: string;
  folder: string;
}

const FileUpload: React.FC<IFileUploadProp> = ({
  field,
  form,
  message,
  accept,
  folder,
}) => {
  const [loader, setLoader] = React.useState(false);
  const handelMedia = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setLoader(true);
    const fileObj = event.target.files && event.target.files[0];
    if (!fileObj) {
      return;
    }
    event.target.value = "";
    const fd = new FormData();
    fd.append("file", fileObj);
    fd.append("folder", folder);
    const res = await uploadMedia(fd);
    if (res.url !== undefined) {
      form.setValue(field, res.url);
    }
    setLoader(false);
  };
  return (
    <div className="bg-[#fafafa] dark:bg-[#272B30] flex flex-col gap-3 rounded-lg">
      <FormField
        name={field}
        control={form.control}
        render={() => (
          <>
            <div className="flex items-center justify-center w-full cursor-pointer">
              <label className="flex flex-col w-full border-2 border-gray-200 border-dashed rounded-lg cursor-pointer  ">
                {loader ? (
                  <div className="flex flex-col gap-4 justify-center items-center py-7">
                    <ReloadIcon className="w-8 h-8 mr-2 animate-spin" />
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center pt-7">
                    <FileImage className="w-12 h-12 text-deep-purple-500" />
                    <p className="py-1 text-sm font-medium tracking-widest text-gray-500 ">
                      Select your
                      <span className="text-deep-purple-300"> file </span>
                      here
                    </p>
                    <p className="mt-1 text-xs font-medium tracking-widest text-gray-500 ">
                      {message}
                    </p>
                  </div>
                )}
                <input
                  type="file"
                  className="h-1 mb-3 opacity-0"
                  accept={accept}
                  onChange={handelMedia}
                />
              </label>
            </div>
            <FormMessage />
          </>
        )}
      />
      {form.watch(field) !== undefined && (
        <div className="relative flex w-full  justify-center h-[200px] bg-neutral-white rounded-lg">
          <Image
            src={`${imgUrl}/${form.watch(field)}`}
            fill
            alt="product"
            unoptimized
            className="w-full h-full object-contain"
          />
        </div>
      )}
    </div>
  );
};

export default FileUpload;
