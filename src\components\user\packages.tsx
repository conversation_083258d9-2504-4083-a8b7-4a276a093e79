import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package2 } from "lucide-react";
import { Badge } from "../ui/badge";
import { NoResults } from "@/loader/no-results";
import Progress from "../common/progress";

const Packages = ({ data }: { data: Package[] }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <Package2 className="h-5 w-5" />
          Packages
        </CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <NoResults
            title=""
            description="There are No purchase by this user for this service"
          />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Package</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((el) => (
                <TableRow key={el.id}>
                  <TableCell>{el.package.name}</TableCell>
                  <TableCell>€{el.amount}</TableCell>
                  <TableCell>
                    <Badge variant="success" className="capitalize">
                      {el.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Progress id={el.id} type="package" status={el.progress} />
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(el.createdAt).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default Packages;
