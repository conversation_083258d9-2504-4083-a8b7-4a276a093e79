import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";
import { getServerSession } from "next-auth";

export const getMentors = async () => {
  const res = await fetch(`${apiUrl}/mentor?page=0&limit=0`, {
    next: {
      tags: ["mentors"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IMentor[];
  }

  return [];
};
export const getMentorById = async (id: string) => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/mentor/admin/${id}`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["mentor", id],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IMentorInfo;
  }

  return null;
};

// ...........................ContactUs........................................

export const getContactUs = async () => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/contact-us?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["contact-us"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IContactUs[];
  }

  return [];
};

// ........................ Blog....................................
export const getBlogs = async () => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/blog?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["blogs"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IBlog[];
  }

  return [];
};
export const getBlog = async (slug: string) => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/blog/${slug}`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["blog", slug],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IBlog;
  }

  return null;
};
// ........................ Customer Review....................................
export const getCustomerReviews = async () => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/customer-review?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["customer-review"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as ICustomerReview[];
  }

  return [];
};
export const getCustomerReview = async (id: string) => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/customer-review/${id}`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["customer-review", id],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as ICustomerReview;
  }

  return null;
};

// ...................... User .............................

export const getUsers = async () => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/user/admin?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["users"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IBlog[];
  }

  return [];
};
export const getUser = async (id: string) => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/user/admin/${id}`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["user", id],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IUser;
  }

  return null;
};

// ............Packages.......................

export const getPackages = async () => {
  const res = await fetch(`${apiUrl}/packages`, {
    next: {
      tags: ["packages"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IPackage[];
  }

  return [];
};

// ............Immigration.......................
export const getImmigrations = async () => {
  const res = await fetch(`${apiUrl}/immigration`, {
    next: {
      tags: ["immigrations"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IImmigration[];
  }

  return [];
};
// ............Training.......................
export const getTrainings = async () => {
  const res = await fetch(`${apiUrl}/training`, {
    next: {
      tags: ["trainings"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as ITraining[];
  }

  return [];
};

// ............Dashboard.......................

export const getDashboard = async () => {
  const session = await getServerSession(authOptions);
  const res = await fetch(`${apiUrl}/dashboard`, {
    headers: {
      Authorization: `Bearer ${session?.backendTokens.accessToken}`,
    },
    next: {
      tags: ["dashboard"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IDashboard;
  }

  return null;
};

// ................. Guest.........................
export const getGuestPurchaseData = async () => {
  const session = await getServerSession(authOptions);

  const [
    servicesResponse,
    packagesResponse,
    immigrationResponse,
    trainingResponse,
  ] = await Promise.all([
    fetch(`${apiUrl}/guest/purchase/service?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session?.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-services"],
      },
      cache: "no-store",
    }),
    fetch(`${apiUrl}/guest/purchase/package?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session?.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-packages"],
      },
      cache: "no-store",
    }),
    fetch(`${apiUrl}/guest/purchase/immigration?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session?.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-immigration"],
      },
      cache: "no-store",
    }),
    fetch(`${apiUrl}/guest/purchase/training?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session?.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-training"],
      },
      cache: "no-store",
    }),
  ]);

  const [servicesData, packagesData, immigrationData, trainingData] =
    await Promise.all([
      servicesResponse.json(),
      packagesResponse.json(),
      immigrationResponse.json(),
      trainingResponse.json(),
    ]);

  return {
    services:
      servicesResponse.status === 200 ? (servicesData as IGuestService[]) : [],
    packages:
      packagesResponse.status === 200 ? (packagesData as IGuestPackage[]) : [],
    immigration:
      immigrationResponse.status === 200
        ? (immigrationData as IGuestImmigration[])
        : [],
    trainings:
      trainingResponse.status === 200 ? (trainingData as IGuestTraining[]) : [],
  };
};
