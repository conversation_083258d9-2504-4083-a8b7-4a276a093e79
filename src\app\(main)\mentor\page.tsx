import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { PlusIcon } from "lucide-react";
import { getMentors } from "@/hooks/use-server";
import Mentors from "@/components/mentor/mentors";

const MentorPage = async () => {
  const data = await getMentors();
  return (
    <ContentLayout title="Mentor">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Mentor</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Link
          href="/mentor/create"
          className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black"
        >
          <PlusIcon />
        </Link>
      </div>
      <Content>
        <Mentors data={data} />
      </Content>
    </ContentLayout>
  );
};

export default MentorPage;
