"use client";
import { useProgress } from "@/hooks/use-query";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "../ui/badge";

interface IProgressProp {
  id: string;
  status: string;
  type: string;
}

const Progress: React.FC<IProgressProp> = ({ id, status, type }) => {
  const { mutate } = useProgress();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="none"
          className="select-none"
          aria-label="Open edit menu"
        >
          <Badge
            //  @ts-ignore

            variant={progress.find((item) => item.name === status)?.variant}
          >
            {status}
          </Badge>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {progress.map((el) => (
          <DropdownMenuItem
            key={el.name}
            className="w-full text-center cursor-pointer"
            onClick={() =>
              mutate({
                status: el.name,
                type,
                id,
              })
            }
          >
            <Badge
              //  @ts-ignore
              variant={el.variant}
              className="w-full text-center flex justify-center"
            >
              {el.name}
            </Badge>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Progress;

const progress = [
  {
    name: "Pending",
    variant: "pending",
  },
  {
    name: "Completed",
    variant: "success",
  },
];
