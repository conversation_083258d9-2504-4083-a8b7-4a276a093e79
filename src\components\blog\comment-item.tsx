"use client";

import React from "react";
import { formatDistanceToNow } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Trash } from "lucide-react";
import { isValidUrl } from "@/utils/tools";
import { imgUrl } from "@/utils/urls";
import { useRemoveComments } from "@/hooks/use-query";

interface CommentItemProps {
  comment: IComment;
  level: number;
}

export function CommentItem({ comment, level }: CommentItemProps) {
  const formattedDate = formatDistanceToNow(new Date(comment.createdAt), {
    addSuffix: true,
  });

  const { mutate, isPending } = useRemoveComments();

  return (
    <div className="group">
      <div className="flex gap-4">
        <Avatar className="w-16 h-16">
          <AvatarImage
            src={
              isValidUrl(comment.author.image || "")
                ? `${comment.author.image}`
                : `${imgUrl}${comment.author.image}`
            }
            alt={comment.author.name}
          />
          <AvatarFallback>
            {comment.author.name.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <span className="font-medium">{comment.author.name}</span>
            <span className="text-xs text-muted-foreground">
              {formattedDate}
            </span>
          </div>

          <div className="text-sm">{comment.content}</div>

          {/* Only show reply button for top-level comments (level 0) */}

          <div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs text-[#fd381d] hover:text-[#fd381d]"
              onClick={() => mutate(comment.id)}
              disabled={isPending}
            >
              <Trash className="mr-1 h-3 w-3" />
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Render replies - simplified since replies are only at one level */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4 pl-4 md:pl-12 border-l-2 border-[#404bd0]/10">
          {comment.replies.map((reply) => (
            <div key={reply.id} className="mb-4">
              <CommentItem comment={reply} level={level + 1} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
