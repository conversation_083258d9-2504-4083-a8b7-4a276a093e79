"use client";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import { formatCurrency } from "@/utils/tools";

const Breakdown = ({ data }: { data: IDashboard }) => {
  const revenueData = [
    {
      name: "Mentor Services",
      value: Number(data.mentor_service_revenue),
      color: "#0ea5e9", // sky-500
    },
    {
      name: "Packages",
      value: Number(data.package_revenue),
      color: "#8b5cf6", // violet-500
    },
    {
      name: "Immigration",
      value: Number(data.immigration_service_revenue),
      color: "#f97316", // orange-500
    },
    {
      name: "Training",
      value: Number(data.training_revenue),
      color: "#AEEA94", // orange-500
    },
  ];
  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Revenue Breakdown</CardTitle>
        <CardDescription>
          Distribution of revenue across different services
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex h-[350px] items-center justify-center">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={revenueData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
                label={({ name, percent }) =>
                  `${name} ${(percent * 100).toFixed(0)}%`
                }
                labelLine={true}
              >
                {revenueData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 flex items-center justify-center gap-4">
          {revenueData.map((entry, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-muted-foreground">
                {entry.name}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default Breakdown;

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-2 shadow-sm">
        <div className="grid grid-cols-2 gap-2">
          <div className="flex flex-col">
            <span className="text-[0.70rem] uppercase text-muted-foreground">
              {payload[0].name}
            </span>
            <span className="font-bold text-muted-foreground">
              {formatCurrency(payload[0].value)}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return null;
};
