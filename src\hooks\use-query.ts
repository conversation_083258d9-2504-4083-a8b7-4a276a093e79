import revalidateTag from "@/utils/revalidate-tag";
import { failed, success } from "@/utils/tools";
import { apiUrl } from "@/utils/urls";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export const useCreateMentor = () => {
  const { data: session } = useSession();
  const router = useRouter();
  return useMutation({
    mutationFn: async (data: IMentor) => {
      const res = await axios.post(`${apiUrl}/mentor/admin/register`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["mentors"]);
      router.push("/mentor");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateMentor = (id: string) => {
  const { data: session } = useSession();
  const router = useRouter();
  return useMutation({
    mutationFn: async (data: IMentor) => {
      const res = await axios.patch(`${apiUrl}/mentor/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["mentors"]);
      revalidateTag(["mentor", data.id]);
      router.push("/mentor");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

export const useDeleteMentor = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/mentor/admin/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["mentors"]);
      toast.success(`${data.name} removed sucessfully`, success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// ...................Service..................................

export const useCreateService = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IService) => {
      const res = await axios.post(`${apiUrl}/services/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Services Added", success);
      revalidateTag(["mentor", data.id]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateService = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IService) => {
      const res = await axios.patch(`${apiUrl}/services/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Mentor Service Updated", success);
      revalidateTag(["mentor", data.id]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useDeleteService = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/services/admin/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success(`${data.name} removed`, success);
      revalidateTag(["mentor", data.id]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// .................... Blog ......................................

export const useCreateBlog = () => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IBlog) => {
      const res = await axios.post(`${apiUrl}/blog`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Blog Added", success);
      revalidateTag(["blogs"]);
      router.push("/blog");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateBlog = (id: string) => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IBlog) => {
      const res = await axios.patch(`${apiUrl}/blog/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Blog Updated", success);
      revalidateTag(["blog", data.slug]);
      revalidateTag(["blogs"]);
      router.push("/blog");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useDeleteBlog = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/blog/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Blog removed successfully", success);
      revalidateTag(["blogs"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
// .................... Customer Review ......................................

export const useCreateCustomerReview = () => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ICustomerReview) => {
      const res = await axios.post(`${apiUrl}/customer-review`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Customer Review Added", success);
      revalidateTag(["customer-review"]);
      router.push("/customer-review");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useUpdateCustomerReview = (id: string) => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ICustomerReview) => {
      const res = await axios.patch(`${apiUrl}/customer-review/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Customer Review Updated", success);
      revalidateTag(["customer-review", data.id]);
      revalidateTag(["customer-review"]);
      router.push("/customer-review");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useRemoveCustomerReview = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/customer-review/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Customer Review removed successfully", success);
      revalidateTag(["customer-review"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

// ....................Package.....................

export const useCreatePackage = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IPackage) => {
      const res = await axios.post(`${apiUrl}/packages`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Package Added", success);
      revalidateTag(["packages"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdatePackage = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IPackage) => {
      const res = await axios.patch(`${apiUrl}/packages/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Package updated sucessfully", success);
      revalidateTag(["packages"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useRemovePackage = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/packages/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Package removed sucessfully", success);
      revalidateTag(["packages"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// ....................Immigration.....................

export const useCreateImmigration = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IImmigration) => {
      const res = await axios.post(`${apiUrl}/immigration`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Immigration Service Added", success);
      revalidateTag(["immigration"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateImmigration = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IImmigration) => {
      const res = await axios.patch(`${apiUrl}/immigration/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Immigration Service updated sucessfully", success);
      revalidateTag(["immigration"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useRemoveImmigration = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/immigration/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Immigration Service removed sucessfully", success);
      revalidateTag(["immigration"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
// ....................Training.....................

export const useCreateTraining = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ITraining) => {
      const res = await axios.post(`${apiUrl}/training`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Training Service Added", success);
      revalidateTag(["trainings"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useUpdateTraining = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ITraining) => {
      const res = await axios.patch(`${apiUrl}/training/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Training Service updated sucessfully", success);
      revalidateTag(["trainings"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useRemoveTraining = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/training/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Training Service removed sucessfully", success);
      revalidateTag(["trainings"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
// .................. Progress...................

export const useProgress = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: { id: string; status: string; type: string }) => {
      const { type, ...result } = data;
      const res = await axios.patch(
        `${apiUrl}/admin/user/progress/${type}`,
        result,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["user", data.userId]);
      toast.success("Sucessfully updated progress", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useGuestProgress = (validate: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: { id: string; status: string; type: string }) => {
      const { type, ...result } = data;
      const res = await axios.patch(
        `${apiUrl}/admin/user/progress/${type}`,
        result,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag([validate, data.userId]);
      toast.success("Sucessfully updated progress", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// ..............User...............

export const useCreateUser = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: {
      name: string;
      email: string;
      password?: string;
    }) => {
      const res = await axios.post(`${apiUrl}/user/admin`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["users"]);
      toast.success("Admin added new user sucessfully", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useUpdateUser = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: {
      name: string;
      email: string;
      password?: string;
    }) => {
      const res = await axios.patch(`${apiUrl}/user/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["users"]);
      toast.success("Admin updated user detail sucessfully", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

export const useRemoveUser = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/user/admin/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("User removed sucessfully", success);
      revalidateTag(["users"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

// ...........Comments...................................
export const useRemoveComments = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/comment/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Comment removed sucessfully", success);
      revalidateTag(["comments", data.blogId]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
