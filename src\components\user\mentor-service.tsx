import React from "react";
import Link from "next/link";
import { GraduationCap } from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { isValidUrl } from "@/utils/tools";
import { imgUrl } from "@/utils/urls";
import { NoResults } from "@/loader/no-results";
import Progress from "../common/progress";

interface MentorServiceProps {
  data: Service[];
}

export default function MentorService({ data }: MentorServiceProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <GraduationCap className="h-5 w-5" />
          Mentor Services
        </CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <NoResults
            title=""
            description="There are No purchase by this user for this service"
          />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Mentor</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((service) => (
                <TableRow key={service?.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="font-medium">
                        {service?.mentor_services?.name}
                      </p>
                      <Link
                        href={service?.mentor_services?.meeting_link ?? ""}
                        className="text-sm text-blue-600 hover:underline inline-block"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Schedule Meeting
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/mentor/detail?id=${service?.mentor_services?.mentor?.id}`}
                      className="flex items-center gap-3 hover:opacity-80 transition-opacity"
                    >
                      <Avatar className="h-10 w-10 border">
                        <AvatarImage
                          src={
                            isValidUrl(service?.mentor_services?.mentor?.image)
                              ? service?.mentor_services?.mentor?.image
                              : `${imgUrl}${service?.mentor_services?.mentor?.image}`
                          }
                          alt={`${service?.mentor_services?.mentor?.name}'s profile picture`}
                          className="object-cover"
                        />
                        <AvatarFallback className="bg-muted">
                          {service?.mentor_services?.mentor?.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium">
                        {service?.mentor_services?.mentor?.name}
                      </span>
                    </Link>
                  </TableCell>
                  <TableCell>€{service.amount}</TableCell>
                  <TableCell>
                    <Badge variant="success" className="capitalize">
                      {service.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Progress
                      id={service.id}
                      type="service"
                      status={service.progress}
                    />
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(service.createdAt).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
